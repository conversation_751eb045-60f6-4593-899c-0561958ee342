import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, ILike } from 'typeorm';
import fetch from 'node-fetch';
import * as FormData from 'form-data';
import { ChatAi } from './entities/chatAi.entity';
import { ChatAiDocument } from './entities/document.entity';
import { ChatAiMessage } from './entities/message.entity';
import { ChatAiCreditUsage } from './entities/credit-usage.entity';
import { ChatAiApiTransaction } from './entities/transaction.entity';
import { InjectRepository } from '@nestjs/typeorm';
import {
  CreateChatAiDto,
  UpdateChatAiDto,
  CreateDocumentDto,
  UpdateDocumentDto,
  CreateChatMessageDto,
  ChatQueryDto,
  FetchSingleChatAiDto,
  FetchSingleDocumentDto,
  FetchDocumentsDto,
  FetchChatAisDto,
  RemoveChatAiDto,
  UpdateChatAiSettingDto,
  RemoveDocumentDto,
} from './dto/chatAi.dto';
import { AppMessage, CommonMessage } from '../CommonMessages/CommonMessages';
import { paginate } from '../utils/common.service';

import { Application } from '../application/entities/application.entity';

// Import document processing services
import { llamaParseService } from './services/llamaparse';
import { llamaIndexService } from './services/llamaindex';
import {
  SecureLogger,
  RateLimitingService,
  FileValidationService,
} from './services/security';
import { VectorService } from '../vector/vector.service';
import { EmbeddingService } from '../vector/embedding.service';

@Injectable()
export class ChatAiService {
  private readonly logger = new Logger(ChatAiService.name, {
    timestamp: true,
  });
  private readonly secureLogger = new SecureLogger();
  private readonly rateLimitingService = new RateLimitingService();
  private readonly fileValidationService = new FileValidationService();

  constructor(
    @InjectRepository(ChatAi)
    private readonly chatAiRepository: Repository<ChatAi>,
    @InjectRepository(ChatAiDocument)
    private readonly documentRepository: Repository<ChatAiDocument>,
    @InjectRepository(ChatAiMessage)
    private readonly messageRepository: Repository<ChatAiMessage>,
    @InjectRepository(ChatAiCreditUsage)
    private readonly creditUsageRepository: Repository<ChatAiCreditUsage>,
    @InjectRepository(ChatAiApiTransaction)
    private readonly apiTransactionRepository: Repository<ChatAiApiTransaction>,
    @InjectRepository(Application)
    private readonly applicationRepository: Repository<Application>,
    private readonly dataSource: DataSource,
    private readonly vectorService: VectorService,
    private readonly embeddingService: EmbeddingService,
  ) {}

  // ==================== File Validation Methods ====================

  private validateFileType(filename: string, contentType: string): boolean {
    const allowedTypes = [
      // PDF
      'application/pdf',
      // Microsoft Office
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      // Text files
      'text/plain',
      'text/csv',
      'text/html',
      'text/markdown',
      'application/rtf',
      'application/xml',
      // Images
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/tiff',
      'image/webp',
    ];

    const fileExtension = filename.toLowerCase().split('.').pop();
    const allowedExtensions = [
      'pdf',
      'doc',
      'docx',
      'ppt',
      'pptx',
      'xls',
      'xlsx',
      'txt',
      'csv',
      'html',
      'htm',
      'md',
      'rtf',
      'xml',
      'jpg',
      'jpeg',
      'png',
      'gif',
      'bmp',
      'tiff',
      'tif',
      'webp',
    ];

    return (
      allowedTypes.includes(contentType) &&
      allowedExtensions.includes(fileExtension || '')
    );
  }

  private validateFileContent(
    fileBuffer: Buffer,
    contentType: string,
    filename: string,
  ): boolean {
    // Magic number validation for security
    const magicNumbers: { [key: string]: number[][] } = {
      'application/pdf': [[0x25, 0x50, 0x44, 0x46]], // %PDF
      'image/jpeg': [[0xff, 0xd8, 0xff]],
      'image/png': [[0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a]],
      'image/gif': [[0x47, 0x49, 0x46, 0x38]], // GIF8
      'application/msword': [[0xd0, 0xcf, 0x11, 0xe0, 0xa1, 0xb1, 0x1a, 0xe1]],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        [[0x50, 0x4b, 0x03, 0x04]], // ZIP signature
    };

    const firstBytes = Array.from(fileBuffer.slice(0, 16));
    const expectedMagicNumbers = magicNumbers[contentType];

    // If no magic numbers defined for this type, allow it (for text files, etc.)
    if (!expectedMagicNumbers || expectedMagicNumbers.length === 0) {
      return true;
    }

    // Check if file starts with any of the expected magic numbers
    return expectedMagicNumbers.some((magic) => {
      return magic.every(
        (byte, index) =>
          index < firstBytes.length && firstBytes[index] === byte,
      );
    });
  }

  private validateFileSize(fileSize: number): boolean {
    const maxSize = 5 * 1024 * 1024; // 5MB limit
    return fileSize <= maxSize;
  }

  // ==================== Main ChatAI CRUD Operations ====================

  async setupChatAi(userId: number, payload: CreateChatAiDto) {
    try {
      // Check if app exists and belongs to user
      const app = await this.applicationRepository.findOne({
        where: { id: payload.appId, user: { id: userId } },
      });
      if (!app) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }
      if (!app.isActive) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppIsInactive,
        };
      }

      // Check if ChatAI already exists for this app
      const existingChatAi = await this.chatAiRepository.findOne({
        where: { app: { id: payload.appId } },
      });
      if (existingChatAi) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AlreadySetup('ChatAI'),
        };
      }

      // Create new ChatAI project
      const newChatAi = this.chatAiRepository.create({
        name: payload.name,
        description: payload.description,
        userId: userId.toString(),
        app: { id: payload.appId },
      });

      await this.chatAiRepository.save(newChatAi);

      // Log credit usage for project creation
      await this.logCreditUsage(
        newChatAi.id,
        userId.toString(),
        'project_create',
        1,
      );

      return {
        error: false,
        statusCode: HttpStatus.CREATED,
        message: AppMessage.AppCreated('ChatAI'),
      };
    } catch (error) {
      this.logger.error(`Setup ChatAI failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async updateChatAi(userId: number, updateChatAiDto: UpdateChatAiDto) {
    try {
      const chatAi = await this.dataSource
        .getRepository(ChatAi)
        .createQueryBuilder('chatAi')
        .leftJoinAndSelect('chatAi.app', 'app')
        .leftJoinAndSelect('app.user', 'user')
        .where('app.id = :appId', { appId: updateChatAiDto.appId })
        .andWhere('user.id = :userId', { userId })
        .getOne();

      if (!chatAi) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }

      // Update ChatAI fields
      if (updateChatAiDto.name) chatAi.name = updateChatAiDto.name;
      if (updateChatAiDto.description !== undefined)
        chatAi.description = updateChatAiDto.description;
      // API keys are now configured globally via environment variables

      await this.chatAiRepository.save(chatAi);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.AppUpdated('ChatAI'),
      };
    } catch (error) {
      this.logger.error(`Update ChatAI failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async updateChatAiSettings(
    userId: number,
    updateChatAiSettingDto: UpdateChatAiSettingDto,
  ) {
    try {
      this.logger.debug(
        `Updating ChatAI settings for user ${userId} with data:`,
        updateChatAiSettingDto,
      );

      const chatAi = await this.dataSource
        .getRepository(ChatAi)
        .createQueryBuilder('chatAi')
        .leftJoinAndSelect('chatAi.app', 'app')
        .leftJoinAndSelect('app.user', 'user')
        .where('app.id = :appId', { appId: updateChatAiSettingDto.appId })
        .andWhere('user.id = :userId', { userId })
        .getOne();

      if (!chatAi) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }

      this.logger.debug(`Current ChatAI isActive status: ${chatAi.isActive}`);

      // Update settings
      if (updateChatAiSettingDto.isActive !== undefined) {
        this.logger.debug(
          `Updating isActive from ${chatAi.isActive} to ${updateChatAiSettingDto.isActive}`,
        );
        chatAi.isActive = updateChatAiSettingDto.isActive;
      }
      if (updateChatAiSettingDto.notificationsEnabled !== undefined)
        chatAi.notificationsEnabled =
          updateChatAiSettingDto.notificationsEnabled;
      if (updateChatAiSettingDto.notificationEmail !== undefined)
        chatAi.notificationEmail = updateChatAiSettingDto.notificationEmail;
      if (updateChatAiSettingDto.credits !== undefined)
        chatAi.credits = updateChatAiSettingDto.credits;
      if (updateChatAiSettingDto.subscriptionStatus !== undefined)
        chatAi.subscriptionStatus = updateChatAiSettingDto.subscriptionStatus;

      const savedChatAi = await this.chatAiRepository.save(chatAi);
      this.logger.debug(
        `Updated ChatAI isActive status: ${savedChatAi.isActive}`,
      );

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.AppUpdated('ChatAI Settings'),
        result: {
          id: savedChatAi.id,
          isActive: savedChatAi.isActive,
          notificationsEnabled: savedChatAi.notificationsEnabled,
          notificationEmail: savedChatAi.notificationEmail,
          credits: savedChatAi.credits,
          subscriptionStatus: savedChatAi.subscriptionStatus,
        },
      };
    } catch (error) {
      this.logger.error(`Update ChatAI settings failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getSingleChatAi(userId: number, query: FetchSingleChatAiDto) {
    try {
      const chatAi = await this.dataSource
        .getRepository(ChatAi)
        .createQueryBuilder('chatAi')
        .leftJoinAndSelect('chatAi.app', 'app')
        .leftJoinAndSelect('app.user', 'user')
        .leftJoinAndSelect('chatAi.documents', 'documents')
        .where('app.id = :appId', { appId: query.appId })
        .andWhere('user.id = :userId', { userId })
        .getOne();

      if (!chatAi) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }

      return {
        error: false,
        statusCode: HttpStatus.OK,
        result: chatAi,
      };
    } catch (error) {
      this.logger.error(`Get single ChatAI failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getAllChatAis(userId: number, query: FetchChatAisDto) {
    try {
      let whereClause: any = {};

      if (query.s) {
        whereClause = {
          ...whereClause,
          name: ILike(`%${query.s}%`),
        };
      }
      const skip = paginate(query.page, query.limit);

      const chatAi = await this.dataSource
        .getRepository(ChatAi)
        .createQueryBuilder('chatAi')
        .innerJoinAndSelect('chatAi.app', 'app', 'app.userId = :id', {
          id: userId,
        })
        .where(whereClause)
        .skip(skip)
        .orderBy('chatAi.createdAt', 'DESC')
        .take(query.limit)
        .getManyAndCount();

      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message:
          chatAi[1] > 0
            ? AppMessage.ServiceFetched('ChatAIs')
            : CommonMessage.NoDataFound,
        result: chatAi[0],
        totalCount: chatAi[1],
      };
    } catch (error) {
      this.logger.error(`Get all ChatAIs failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async removeChatAi(userId: number, removeChatAiDto: RemoveChatAiDto) {
    try {
      const chatAi = await this.dataSource
        .getRepository(ChatAi)
        .createQueryBuilder('chatAi')
        .leftJoinAndSelect('chatAi.app', 'app')
        .leftJoinAndSelect('app.user', 'user')
        .where('app.id = :appId', { appId: removeChatAiDto.appId })
        .andWhere('user.id = :userId', { userId })
        .getOne();

      if (!chatAi) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }

      // Remove all documents from vector database for this app
      try {
        await this.vectorService.deleteAppDocuments(chatAi.app.id);
        console.log(
          `✅ Removed all documents for appId ${chatAi.app.id} from vector database`,
        );
      } catch (vectorError) {
        console.warn(
          `⚠️ Failed to remove app documents from vector database: ${vectorError.message}`,
        );
        // Continue with ChatAI deletion even if vector cleanup fails
      }

      await this.chatAiRepository.remove(chatAi);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.FeatureRemoved('ChatAI'),
      };
    } catch (error) {
      this.logger.error(`Remove ChatAI failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  // ==================== Document Management ====================

  async uploadDocument(
    userId: number,
    file: Express.Multer.File,
    createDocumentDto: CreateDocumentDto,
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // File validation
      if (!file) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: 'No file uploaded',
        };
      }

      // Enhanced file content validation using security service
      if (
        !this.fileValidationService.validateFileContent(
          file.buffer,
          file.mimetype,
          file.originalname,
        )
      ) {
        this.secureLogger.security(
          'File content validation failed',
          {
            userId: userId.toString(),
            endpoint: '/upload-document',
            method: 'POST',
          },
          {
            filename: file.originalname,
            fileSize: file.size,
            contentType: file.mimetype,
          },
        );

        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message:
            "File content doesn't match the file type. Possible malicious file detected.",
        };
      }

      // Enhanced file type validation using security service
      if (
        !this.fileValidationService.validateFileType(
          file.originalname,
          file.mimetype,
        )
      ) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message:
            'Unsupported file type. Please upload documents only: PDF, Word, PowerPoint, Excel, or text files. Images, videos, GIFs, and compressed files (.zip, .rar, .epub, etc.) are not allowed.',
        };
      }

      // Per-user upload tracking (prevents authenticated user abuse)
      if (
        !this.rateLimitingService.trackUserUpload(userId.toString(), file.size)
      ) {
        return {
          error: true,
          statusCode: HttpStatus.TOO_MANY_REQUESTS,
          message: 'Upload limit exceeded. Please try again later.',
        };
      }

      // Validate file size
      if (!this.validateFileSize(file.size)) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: 'File too large. Maximum size allowed is 5MB.',
        };
      }

      // Verify ChatAI project exists and belongs to user
      const chatAi = await this.dataSource
        .getRepository(ChatAi)
        .createQueryBuilder('chatAi')
        .leftJoinAndSelect('chatAi.app', 'app')
        .leftJoinAndSelect('app.user', 'user')
        .where('app.id = :appId', { appId: createDocumentDto.appId })
        .andWhere('user.id = :userId', { userId })
        .getOne();

      if (!chatAi) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }

      // Check credit limits before upload
      const creditLimits = await this.checkCreditLimits(chatAi.id);
      if (!creditLimits.canUploadDocument) {
        return {
          error: true,
          statusCode: HttpStatus.FORBIDDEN,
          message:
            creditLimits.subscriptionStatus === 'free'
              ? `Insufficient credits for document upload. You have ${creditLimits.creditsRemaining} credits remaining. Upgrade to Pro for unlimited uploads!`
              : 'Insufficient credits for document upload',
        };
      }

      // Create document record with "uploading" status
      const document = this.documentRepository.create({
        filename: file.originalname,
        filesize: file.size,
        contentType: file.mimetype,
        status: 'uploading',
        userId: userId.toString(),
        projectId: chatAi.id,
        project: chatAi,
      });

      const savedDocument = await queryRunner.manager.save(document);

      // Deduct credits for document upload
      const creditsDeducted = await this.deductCredits(
        chatAi.id,
        userId.toString(),
        'document_upload',
        1,
        savedDocument.id,
      );

      if (!creditsDeducted && creditLimits.subscriptionStatus === 'free') {
        // Rollback if credit deduction fails
        await queryRunner.rollbackTransaction();
        return {
          error: true,
          statusCode: HttpStatus.FORBIDDEN,
          message:
            'Insufficient credits for document upload. Upgrade to Pro for unlimited uploads!',
        };
      }

      await queryRunner.commitTransaction();

      // Process document asynchronously
      this.processDocumentAsync(
        savedDocument.id,
        file.buffer,
        file.originalname,
      );

      return {
        error: false,
        statusCode: HttpStatus.CREATED,
        message: 'Document upload started successfully',
        result: {
          documentId: savedDocument.id,
          filename: savedDocument.filename,
          status: savedDocument.status,
        },
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Upload document failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    } finally {
      await queryRunner.release();
    }
  }

  async getDocuments(userId: number, query: FetchDocumentsDto) {
    try {
      // First verify the ChatAI exists and user has access
      const chatAi = await this.dataSource
        .getRepository(ChatAi)
        .createQueryBuilder('chatAi')
        .leftJoinAndSelect('chatAi.app', 'app')
        .leftJoinAndSelect('app.user', 'user')
        .where('app.id = :appId', { appId: query.appId })
        .andWhere('user.id = :userId', { userId })
        .getOne();

      if (!chatAi) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }

      // Apply pagination
      const skip = paginate(query.page, query.limit);

      // Get documents with pagination
      const [documents, totalCount] = await this.dataSource
        .getRepository(ChatAiDocument)
        .findAndCount({
          where: {
            project: { id: chatAi.id },
          },
          take: query.limit,
          skip: skip,
          order: { createdAt: 'DESC' },
        });

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message:
          totalCount > 0
            ? AppMessage.ServiceFetched('Documents')
            : CommonMessage.NoDataFound,
        result: documents,
        totalCount: totalCount,
      };
    } catch (error) {
      this.logger.error(`Get documents failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async updateDocument(userId: number, updateDocumentDto: UpdateDocumentDto) {
    try {
      // Find document and verify ownership
      const document = await this.dataSource
        .getRepository(ChatAiDocument)
        .createQueryBuilder('document')
        .leftJoinAndSelect('document.project', 'project')
        .leftJoinAndSelect('project.app', 'app')
        .leftJoinAndSelect('app.user', 'user')
        .where('document.id = :documentId', {
          documentId: updateDocumentDto.documentId,
        })
        .andWhere('user.id = :userId', { userId })
        .getOne();

      if (!document) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: 'Document not found or access denied',
        };
      }

      // Update document metadata
      if (updateDocumentDto.title !== undefined) {
        // Note: We don't have title field in entity, but we can add it or use filename
        document.filename = updateDocumentDto.title;
      }
      if (updateDocumentDto.status !== undefined) {
        document.status = updateDocumentDto.status;
      }

      await this.documentRepository.save(document);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: 'Document updated successfully',
        result: document,
      };
    } catch (error) {
      this.logger.error(`Update document failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async removeDocument(userId: number, removeDocumentDto: RemoveDocumentDto) {
    try {
      console.log(
        `🗑️ Starting document deletion process for documentId: ${removeDocumentDto.documentId}, appId: ${removeDocumentDto.appId}, userId: ${userId}`,
      );

      // Find document and verify ownership
      const document = await this.dataSource
        .getRepository(ChatAiDocument)
        .createQueryBuilder('document')
        .leftJoinAndSelect('document.project', 'project')
        .leftJoinAndSelect('project.app', 'app')
        .leftJoinAndSelect('app.user', 'user')
        .where('document.id = :documentId', {
          documentId: removeDocumentDto.documentId,
        })
        .andWhere('app.id = :appId', { appId: removeDocumentDto.appId })
        .andWhere('user.id = :userId', { userId })
        .getOne();

      if (!document) {
        console.log(
          `❌ Document not found or access denied for documentId: ${removeDocumentDto.documentId}`,
        );
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Document not found or access denied',
        };
      }

      console.log(
        `✅ Document found: ${document.filename} (ID: ${document.id}) for app: ${document.project.app.id}`,
      );

      // Remove from vector database first (with app isolation for security)
      try {
        const deletedCount = await this.vectorService.deleteDocument(
          document.id.toString(),
          document.project.app.id,
        );
        console.log(
          `✅ Removed document ${document.id} from vector database (${deletedCount} chunks deleted)`,
        );
      } catch (vectorError) {
        console.warn(
          `⚠️ Failed to remove document from vector database: ${vectorError.message}`,
        );
        // Continue with document deletion even if vector cleanup fails
      }

      // Also notify ChatAI-SDK-Clean to ensure complete cleanup
      try {
        await this.notifyChatAISDKDocumentDeletion(
          document.id.toString(),
          document.project.app.id,
        );
      } catch (sdkError) {
        console.warn(
          `⚠️ Failed to notify ChatAI-SDK-Clean of document deletion: ${sdkError.message}`,
        );
        // Continue with document deletion even if SDK notification fails
      }

      // Remove the document from database
      await this.documentRepository.remove(document);
      console.log(
        `✅ Document ${document.id} removed from PostgreSQL database`,
      );

      console.log(
        `🎉 Document deletion completed successfully for documentId: ${removeDocumentDto.documentId}`,
      );

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message:
          'Document deleted successfully.',
      };
    } catch (error) {
      this.logger.error(`Remove document failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  // ==================== Private Helper Methods ====================

  /**
   * Background document processing using reference implementation flow
   */
  private async processDocumentAsync(
    documentId: number,
    fileBuffer: Buffer,
    filename: string,
  ) {
    try {
      console.log(
        `🔄 Starting background processing for document: ${documentId}`,
      );

      // Get document with project relation to access appId
      const document = await this.documentRepository.findOne({
        where: { id: documentId },
        relations: ['project', 'project.app'],
      });

      if (!document) {
        throw new Error(`Document not found: ${documentId}`);
      }

      const appId = document.project.app.id;
      console.log(`📋 Processing document for appId: ${appId}`);

      // Clean Separation: Let ChatAI-SDK-Clean handle ALL processing and status updates
      console.log(
        `� Sending document to ChatAI-SDK-Clean for complete processing: ${filename}`,
      );
      console.log(
        `🎯 ChatAI-SDK-Clean will handle: parsing → embedding → indexing → ready`,
      );

      const processingResult = await this.processDocumentInChatAI(
        fileBuffer,
        filename,
        appId,
        documentId.toString(),
        document.userId,
      );

      console.log(`✅ Document processing initiated in ChatAI-SDK-Clean`);
      console.log(
        `📊 ChatAI-SDK-Clean will update status automatically via internal API`,
      );

      // Note: Status updates will be handled by ChatAI-SDK-Clean via internal API
      // No manual status updates needed here - clean separation!

      console.log(`✅ Document processing completed: ${documentId}`);
    } catch (error) {
      console.error(
        `❌ Background processing failed for ${documentId}:`,
        (error as Error).message,
      );

      // Update document with error status
      await this.documentRepository.update(documentId, {
        status: 'error',
        errorMessage: (error as Error).message,
      });
    }
  }

  /**
   * Process document in ChatAI-SDK-Clean (LlamaIndex + Vector DB)
   */
  private async processDocumentInChatAI(
    fileBuffer: Buffer,
    filename: string,
    appId: string,
    documentId: string,
    userId: string,
  ): Promise<any> {
    try {
      console.log(
        `📊 Sending document to ChatAI-SDK-Clean for complete processing: ${filename}`,
      );

      // Prepare form data for file upload
      const formData = new FormData();

      formData.append('file', fileBuffer, {
        filename: filename,
        contentType: this.getContentType(filename),
      });
      formData.append('appId', appId);
      formData.append('documentId', documentId);
      formData.append('userId', userId);
      formData.append('filename', filename);

      // Call ChatAI-SDK-Clean complete processing API
      const vectorProcessingUrl =
        process.env.CHATAI_SDK_URL || 'http://localhost:3001';
      const response = await fetch(
        `${vectorProcessingUrl}/api/vector/upload-and-process`,
        {
          method: 'POST',
          headers: {
            'x-api-key': appId, // Use appId as API key for authentication
            ...formData.getHeaders(),
          },
          body: formData,
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `ChatAI-SDK-Clean processing failed: ${response.status} - ${errorText}`,
        );
      }

      const result = (await response.json()) as any;

      if (!result.success) {
        throw new Error(
          `ChatAI-SDK-Clean processing failed: ${result.error || 'Unknown error'}`,
        );
      }

      console.log(
        `🎉 Document processed successfully in ChatAI-SDK-Clean: ${result.data.vectorProcessing.storedChunks} chunks stored`,
      );

      return result;
    } catch (error) {
      console.error(
        '❌ Failed to process document in ChatAI-SDK-Clean:',
        error.message,
      );
      throw new Error(`ChatAI-SDK-Clean processing failed: ${error.message}`);
    }
  }

  /**
   * Get content type based on filename
   */
  private getContentType(filename: string): string {
    const ext = filename.toLowerCase().split('.').pop();
    const contentTypes: { [key: string]: string } = {
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      txt: 'text/plain',
      md: 'text/markdown',
      html: 'text/html',
      rtf: 'application/rtf',
    };

    return contentTypes[ext || ''] || 'application/octet-stream';
  }

  /**
   * Store document text in Qdrant vector database with proper tenant isolation
   * @deprecated - Now handled by ChatAI-SDK-Clean
   */
  private async storeDocumentInVectorDB(
    text: string,
    appId: string,
    documentId: string,
    filename: string,
    userId: string,
    pageCount?: number,
    wordCount?: number,
  ): Promise<void> {
    try {
      console.log(
        `📊 Sending document to ChatAI-SDK-Clean for vector processing: ${filename}`,
      );

      // Call ChatAI-SDK-Clean vector processing API
      const vectorProcessingUrl =
        process.env.CHATAI_SDK_URL || 'http://localhost:3001';
      const response = await fetch(
        `${vectorProcessingUrl}/api/vector/process-document`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': appId, // Use appId as API key for authentication
          },
          body: JSON.stringify({
            documentId,
            appId,
            filename,
            userId,
            parsedText: text,
            metadata: {
              pageCount,
              wordCount,
            },
          }),
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `Vector processing API failed: ${response.status} - ${errorText}`,
        );
      }

      const result = (await response.json()) as any;

      if (!result.success) {
        throw new Error(
          `Vector processing failed: ${result.error || 'Unknown error'}`,
        );
      }

      console.log(
        `🎉 Document processed for vector search: ${result.data.storedChunks}/${result.data.totalChunks} chunks stored`,
      );
    } catch (error) {
      console.error(
        '❌ Failed to process document for vector search:',
        error.message,
      );
      // Don't throw error - let document processing continue even if vector storage fails
      console.warn(
        '⚠️ Document processing will continue without vector storage',
      );
    }
  }

  // ==================== Chat Management ====================
  // Note: Chat functionality has been moved to ChatAI-SDK service
  // This service now only handles document management and app validation

  /**
   * Get chat history for a project
   */
  async getChatHistory(userId: number, query: FetchSingleChatAiDto) {
    try {
      // Find the ChatAI application
      const chatAi = await this.chatAiRepository.findOne({
        where: { id: query.appId, userId: userId.toString() },
      });

      if (!chatAi) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: AppMessage.AppNotFound(),
        };
      }

      // Get chat messages
      const messages = await this.messageRepository.find({
        where: { chatAiId: query.appId },
        order: { timestamp: 'DESC' },
        take: 50, // Limit to last 50 messages
      });

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: 'Chat history retrieved successfully',
        data: {
          messages: messages.reverse(), // Show oldest first
          totalMessages: messages.length,
        },
      };
    } catch (error) {
      this.logger.error('Get chat history failed:', error);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Failed to retrieve chat history',
      };
    }
  }

  /**
   * Create a manual message entry
   */
  async createMessage(
    userId: number,
    createChatMessageDto: CreateChatMessageDto,
  ) {
    try {
      // Find the ChatAI application
      const chatAi = await this.chatAiRepository.findOne({
        where: { id: createChatMessageDto.appId, userId: userId.toString() },
      });

      if (!chatAi) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: AppMessage.AppNotFound(),
        };
      }

      // Create message
      const message = this.messageRepository.create({
        chatAiId: createChatMessageDto.appId,
        question: createChatMessageDto.message,
        response: '', // Manual messages don't have AI responses initially
        timestamp: new Date(),
      });

      const savedMessage = await this.messageRepository.save(message);

      return {
        error: false,
        statusCode: HttpStatus.CREATED,
        message: 'Message created successfully',
        data: savedMessage,
      };
    } catch (error) {
      this.logger.error('Create message failed:', error);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Failed to create message',
      };
    }
  }

  // // ==================== Transaction & Credit Management (Placeholder) ====================

  // async getAllTransactions(userId: number, query: GetChatAiTransactionDto) {
  //   // TODO: Implement get transactions logic
  //   return {
  //     error: false,
  //     statusCode: HttpStatus.OK,
  //     result: [],
  //   };
  // }

  async getSingleDocument(userId: number, query: FetchSingleDocumentDto) {
    try {
      // Find the specific document and verify ownership through the app relationship
      const document = await this.dataSource
        .getRepository(ChatAiDocument)
        .createQueryBuilder('document')
        .leftJoinAndSelect('document.project', 'project')
        .leftJoinAndSelect('project.app', 'app')
        .leftJoinAndSelect('app.user', 'user')
        .where('document.id = :documentId', { documentId: query.documentId })
        .andWhere('app.id = :appId', { appId: query.appId })
        .andWhere('user.id = :userId', { userId })
        .getOne();

      if (!document) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Document not found or access denied',
        };
      }

      return {
        error: false,
        statusCode: HttpStatus.OK,
        result: document,
      };
    } catch (error) {
      this.logger.error(`Get single document failed: ${error.message}`);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  // ==================== Internal APIs (for ChatAI-SDK-Clean) ====================

  /**
   * Notify ChatAI-SDK-Clean about document deletion for complete cleanup
   */
  private async notifyChatAISDKDocumentDeletion(
    documentId: string,
    appId: string,
  ): Promise<void> {
    try {
      const vectorProcessingUrl =
        process.env.CHATAI_SDK_URL || 'http://localhost:3001';
      const response = await fetch(
        `${vectorProcessingUrl}/api/vector/document/${documentId}?appId=${appId}`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'x-internal-api-key':
              process.env.INTERNAL_API_KEY || 'chatai-internal-2024',
          },
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `ChatAI-SDK-Clean deletion failed: ${response.status} - ${errorText}`,
        );
      }

      const result = await response.json();
      console.log(
        `✅ ChatAI-SDK-Clean confirmed deletion: ${result.data?.deletedChunks || 0} chunks`,
      );
    } catch (error) {
      console.error(`❌ Failed to notify ChatAI-SDK-Clean: ${error.message}`);
      throw error;
    }
  }

  /**
   * Internal method for ChatAI-SDK-Clean to update document status
   */
  async updateDocumentStatusInternal(updateData: {
    appId: string;
    documentId: string;
    status: string;
    message?: string;
    parsedData?: any;
    pageCount?: number;
    wordCount?: number;
    indexId?: string;
  }) {
    try {
      console.log(
        `📡 Internal status update: Document ${updateData.documentId} → ${updateData.status}`,
      );

      // Find document by documentId and appId
      const document = await this.dataSource
        .getRepository(ChatAiDocument)
        .createQueryBuilder('document')
        .leftJoinAndSelect('document.project', 'project')
        .leftJoinAndSelect('project.app', 'app')
        .where('document.id = :documentId', {
          documentId: updateData.documentId,
        })
        .andWhere('app.id = :appId', { appId: updateData.appId })
        .getOne();

      if (!document) {
        return {
          error: true,
          statusCode: 404,
          message: 'Document not found',
        };
      }

      // Prepare update data
      const updateFields: any = {
        status: updateData.status,
      };

      // Note: processingMessage field doesn't exist in entity, skip for now
      // if (updateData.message) {
      //   updateFields.processingMessage = updateData.message;
      // }

      if (updateData.parsedData) {
        updateFields.parsedData = updateData.parsedData;
      }

      if (updateData.pageCount) {
        updateFields.pageCount = updateData.pageCount;
      }

      if (updateData.wordCount) {
        updateFields.wordCount = updateData.wordCount;
      }

      if (updateData.indexId) {
        updateFields.indexId = updateData.indexId;
      }

      // Update document
      await this.documentRepository.update(document.id, updateFields);

      console.log(
        `✅ Document status updated: ${updateData.documentId} → ${updateData.status}`,
      );

      return {
        error: false,
        statusCode: 200,
        message: 'Document status updated successfully',
        result: {
          documentId: updateData.documentId,
          status: updateData.status,
        },
      };
    } catch (error) {
      console.error(`❌ Internal status update failed: ${error.message}`);
      return {
        error: true,
        statusCode: 500,
        message: 'Internal status update failed',
      };
    }
  }

  // // ==================== Helper Methods ====================

  private async checkCreditLimits(chatAiId: string): Promise<{
    canUploadDocument: boolean;
    canUseChat: boolean;
    creditsRemaining: number;
    subscriptionStatus: string;
  }> {
    const chatAi = await this.chatAiRepository.findOne({
      where: { id: chatAiId },
    });

    if (!chatAi) {
      return {
        canUploadDocument: false,
        canUseChat: false,
        creditsRemaining: 0,
        subscriptionStatus: 'free',
      };
    }

    const creditsRemaining = chatAi.credits;
    const subscriptionStatus = chatAi.subscriptionStatus;

    // Pro and enterprise users have unlimited credits
    if (subscriptionStatus === 'pro' || subscriptionStatus === 'enterprise') {
      return {
        canUploadDocument: true,
        canUseChat: true,
        creditsRemaining: creditsRemaining,
        subscriptionStatus,
      };
    }

    // Free users need credits for actions
    return {
      canUploadDocument: creditsRemaining >= 1, // 1 credit per document upload
      canUseChat: creditsRemaining >= 1, // 1 credit per chat message
      creditsRemaining,
      subscriptionStatus,
    };
  }

  private async deductCredits(
    chatAiId: string,
    userId: string,
    actionType: string,
    creditsToDeduct: number = 1,
    actionId?: number,
  ): Promise<boolean> {
    const chatAi = await this.chatAiRepository.findOne({
      where: { id: chatAiId },
    });

    if (!chatAi) {
      return false;
    }

    // Pro and enterprise users don't deduct credits
    if (
      chatAi.subscriptionStatus === 'pro' ||
      chatAi.subscriptionStatus === 'enterprise'
    ) {
      // Still log the usage for analytics
      await this.logCreditUsage(chatAiId, userId, actionType, 0, actionId);
      return true;
    }

    // Check if user has enough credits
    if (chatAi.credits < creditsToDeduct) {
      return false;
    }

    // Deduct credits
    chatAi.credits -= creditsToDeduct;
    await this.chatAiRepository.save(chatAi);

    // Log credit usage
    await this.logCreditUsage(
      chatAiId,
      userId,
      actionType,
      creditsToDeduct,
      actionId,
    );

    return true;
  }

  private async logCreditUsage(
    chatAiId: string,
    userId: string,
    actionType: string,
    creditsUsed: number,
    actionId?: number,
  ) {
    const creditUsage = this.creditUsageRepository.create({
      chatAiId,
      userId,
      actionType,
      creditsUsed,
      actionId,
    });
    await this.creditUsageRepository.save(creditUsage);
  }
}
